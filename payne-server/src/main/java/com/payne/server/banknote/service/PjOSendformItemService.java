package com.payne.server.banknote.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.server.banknote.entity.PjOSendformItem;

import java.util.List;

/**
 * 送评单明细Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PjOSendformItemService extends IService<PjOSendformItem> {
    
    /**
     * 根据送评单号查询明细列表
     */
    List<PjOSendformItem> listBySendnum(String sendnum);
    
    /**
     * 根据送评单号删除明细
     */
    boolean deleteBySendnum(String sendnum);
    
    /**
     * 批量保存钱币明细
     */
    boolean saveBatchItems(String sendnum, List<PjOSendformItem> items);

    /**
     * 根据送评条码查询钱币详情
     */
    PjOSendformItem getByDiyCode(String diyCode);
}