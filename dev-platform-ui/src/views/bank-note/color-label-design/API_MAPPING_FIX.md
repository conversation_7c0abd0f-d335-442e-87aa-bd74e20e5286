# 彩签设计API接口修复说明

## 问题描述

前端API接口与后端Controller不对应，导致接口调用失败。

## 修复内容

### 1. 修复的接口映射

#### ✅ 创建彩签设计接口
**修复前：**
```javascript
// 前端只有一个通用创建接口
export function createColorLabelDesign(data) {
  return request.post(`${API_BASE}/create`, data);
}
```

**修复后：**
```javascript
// 对应后端的三个具体创建接口
export function createColorLabelBySendform(sendformNumber, templateId) {
  return request.post(`${API_BASE}/create-by-sendform`, { sendformNumber, templateId });
}

export function createColorLabelByDiyCode(diyCode, templateId) {
  return request.post(`${API_BASE}/create-by-diycode`, { diyCode, templateId });
}

export function createColorLabelByCoins(coinIds, templateId) {
  return request.post(`${API_BASE}/create-by-coins`, { coinIds, templateId });
}

// 保留通用接口，内部根据参数自动选择具体接口
export function createColorLabelDesign(data) {
  if (data.sendformNumber && data.templateId) {
    return createColorLabelBySendform(data.sendformNumber, data.templateId);
  } else if (data.diyCode && data.templateId) {
    return createColorLabelByDiyCode(data.diyCode, data.templateId);
  } else if (data.coinIds && data.templateId) {
    return createColorLabelByCoins(data.coinIds, data.templateId);
  } else {
    throw new Error('缺少必要的创建参数');
  }
}
```

#### ✅ 修复API基础路径
**修复前：**
```javascript
const API_BASE = '/color-label-design';
```

**修复后：**
```javascript
const API_BASE = '/api/color-label-design';
```

#### ✅ 更新接口处理
**修复前：**
```javascript
export function updateColorLabelDesign(id, data) {
  return request.put(`${API_BASE}/${id}`, data);
}
```

**修复后：**
```javascript
// 后端暂未实现PUT接口，使用保存接口代替
export function updateColorLabelDesign(id, data) {
  return saveColorLabelDesign({ ...data, id });
}
```

### 2. 标记未实现的接口

对于后端暂未实现的接口，添加了警告和临时处理：

```javascript
// 以下接口后端暂未实现，预留给未来扩展使用

export function getColorLabelDesignStatistics(designId) {
  console.warn('getColorLabelDesignStatistics: 后端接口暂未实现');
  return Promise.resolve({
    designId,
    usageCount: 0,
    lastUsedTime: null,
    createdTime: new Date().toISOString()
  });
}

export function exportColorLabelDesign(designId) {
  console.warn('exportColorLabelDesign: 后端接口暂未实现');
  return Promise.reject(new Error('导出功能暂未实现'));
}

// ... 其他未实现接口
```

### 3. 更新前端组件调用

修改了 `index.vue` 中的API调用逻辑：

**修复前：**
```javascript
const designData = await createColorLabelDesign({
  baseTemplateId: selectedTemplate.value.id,
  coinData: coinData.value,
  dataSource: dataSourceForm
});
```

**修复后：**
```javascript
let designData;

// 根据数据源类型选择对应的创建接口
if (dataSourceForm.type === 'sendform' && dataSourceForm.sendformNumber) {
  designData = await createColorLabelBySendform(
    dataSourceForm.sendformNumber, 
    selectedTemplate.value.id
  );
} else if (dataSourceForm.type === 'diycode' && dataSourceForm.diyCode) {
  designData = await createColorLabelByDiyCode(
    dataSourceForm.diyCode, 
    selectedTemplate.value.id
  );
} else if (dataSourceForm.selectedCoinIds && dataSourceForm.selectedCoinIds.length > 0) {
  designData = await createColorLabelByCoins(
    dataSourceForm.selectedCoinIds, 
    selectedTemplate.value.id
  );
} else {
  throw new Error('缺少必要的数据源信息');
}
```

## 完整的接口映射表

| 前端API方法 | HTTP方法 | 后端接口路径 | 状态 |
|------------|----------|-------------|------|
| `createColorLabelBySendform` | POST | `/api/color-label-design/create-by-sendform` | ✅ 已匹配 |
| `createColorLabelByDiyCode` | POST | `/api/color-label-design/create-by-diycode` | ✅ 已匹配 |
| `createColorLabelByCoins` | POST | `/api/color-label-design/create-by-coins` | ✅ 已匹配 |
| `saveColorLabelDesign` | POST | `/api/color-label-design/save` | ✅ 已匹配 |
| `getColorLabelDesign` | GET | `/api/color-label-design/{id}` | ✅ 已匹配 |
| `deleteColorLabelDesign` | DELETE | `/api/color-label-design/{id}` | ✅ 已匹配 |
| `previewColorLabelDesign` | POST | `/api/color-label-design/preview` | ✅ 已匹配 |
| `generateColorLabelPrintData` | POST | `/api/color-label-design/{id}/generate-print-data` | ✅ 已匹配 |
| `getColorLabelDesignHistory` | GET | `/api/color-label-design/history` | ✅ 已匹配 |
| `copyColorLabelDesign` | POST | `/api/color-label-design/{id}/copy` | ✅ 已匹配 |
| `getColorLabelDesignTemplates` | GET | `/api/color-label-design/templates` | ✅ 已匹配 |
| `applyColorLabelDesignTemplate` | POST | `/api/color-label-design/templates/{templateId}/apply` | ✅ 已匹配 |
| `getAvailableColorLabelElements` | GET | `/api/color-label-design/elements` | ✅ 已匹配 |
| `validateColorLabelDesign` | POST | `/api/color-label-design/validate` | ✅ 已匹配 |
| `uploadColorLabelImage` | POST | `/api/color-label-design/upload-image` | ✅ 已匹配 |
| `updateColorLabelDesign` | PUT | - | ⚠️ 后端未实现，使用save代替 |
| `getCoinsBySendform` | GET | `/api/banknote/scan/getBySendnum` | ✅ 已修正 |
| `getCoinsByDiyCode` | GET | `/api/banknote/scan/detail/diyCode` | ✅ 已修正 |
| `getLabelTemplates` | GET | `/api/label-design/templates` | ✅ 已修正 |
| `getLabelTemplate` | GET | `/api/label-design/template/{templateId}` | ✅ 已修正 |

## 最新修正（2025-01-15）

### ✅ 修正依赖接口路径

根据后端实际Controller实现，修正了以下接口路径：

1. **获取送评单钱币信息**：
   - 修正前：`/api/sendform/{sendformNumber}/coins`
   - 修正后：`/api/banknote/scan/getBySendnum?sendnum={sendformNumber}`
   - 对应Controller：`ScanInputController.getCoinsBySendnum()`

2. **根据送评条码获取钱币信息**：
   - 修正前：`/api/sendform/coin/{diyCode}`
   - 修正后：`/api/banknote/scan/detail/diyCode?diyCode={diyCode}`
   - 对应Controller：`ScanInputController.getCoinDetailByDiyCode()`

3. **获取标签模板列表**：
   - 修正前：`/api/label-templates`
   - 修正后：`/api/label-design/templates`
   - 对应Controller：`LabelDesignController.getTemplateList()`

4. **获取标签模板详情**：
   - 修正前：`/api/label-templates/{templateId}`
   - 修正后：`/api/label-design/template/{templateId}`
   - 对应Controller：`LabelDesignController.getTemplateById()`

## 注意事项

1. **权限要求**：
   - 扫码相关接口需要 `banknote:scan:*` 权限
   - 标签设计接口需要 `banknote:label:design` 权限
   - 彩签设计接口需要 `banknote:colorlabel:*` 权限

2. **未实现功能**：统计、导入导出、批量应用等功能后端暂未实现
3. **错误处理**：前端已添加适当的错误处理和用户提示
4. **接口格式**：部分接口使用查询参数而非路径参数

## 测试建议

1. 测试三种创建方式：送评单号、送评条码、钱币ID列表
2. 测试保存、预览、打印等核心功能
3. 验证权限控制是否正常工作
4. 检查错误处理和用户提示是否友好
