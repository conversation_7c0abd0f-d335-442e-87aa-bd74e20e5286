<template>
  <ele-page>
    <div class="color-label-design">
      <!-- 步骤导航 -->
      <el-steps :active="currentStep" finish-status="success" class="steps-nav">
        <el-step title="选择数据源" description="输入送评单号或条码"></el-step>
        <el-step title="选择基础模板" description="选择钱币标签模板"></el-step>
        <el-step title="彩签设计" description="在基础模板上设计彩签"></el-step>
        <el-step title="预览打印" description="预览并打印彩签"></el-step>
      </el-steps>

      <!-- 步骤1: 数据源选择 -->
      <div v-if="currentStep === 0" class="step-content">
        <ele-card>
          <template #header>
            <span>选择数据源</span>
          </template>

          <el-form :model="dataSourceForm" label-width="120px">
            <el-form-item label="数据源类型">
              <el-radio-group v-model="dataSourceForm.type">
                <el-radio label="sendform">送评单号</el-radio>
                <el-radio label="diycode">送评条码</el-radio>
                <el-radio label="coins">钱币选择</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="dataSourceForm.type === 'sendform'" label="送评单号">
              <el-input
                v-model="dataSourceForm.sendformNumber"
                placeholder="请输入送评单号"
                style="width: 300px;"
              />
            </el-form-item>

            <el-form-item v-if="dataSourceForm.type === 'diycode'" label="送评条码">
              <el-input
                v-model="dataSourceForm.diyCode"
                placeholder="请输入送评条码"
                style="width: 300px;"
              />
            </el-form-item>

            <el-form-item v-if="dataSourceForm.type === 'coins'" label="钱币选择">
              <el-button @click="showCoinSelector = true">
                选择钱币 (已选择 {{ selectedCoins.length }} 个)
              </el-button>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="loadCoinData" :loading="loading">
                加载钱币数据
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 钱币数据预览 -->
          <div v-if="coinData.length > 0" class="coin-data-preview">
            <h4>钱币数据预览 ({{ coinData.length }} 个钱币)</h4>
            <el-table :data="coinData.slice(0, 5)" size="small" max-height="300">
              <el-table-column prop="serialNumber" label="钱币编号" width="150" />
              <el-table-column prop="itemName" label="钱币名称" min-width="200" />
              <el-table-column prop="gradeLevel" label="品相等级" width="120" />
              <el-table-column prop="customerName" label="客户名称" width="100" />
            </el-table>
            <p v-if="coinData.length > 5" class="more-data-tip">
              还有 {{ coinData.length - 5 }} 个钱币...
            </p>
          </div>
        </ele-card>
      </div>

      <!-- 步骤2: 基础模板选择 -->
      <div v-if="currentStep === 1" class="step-content">
        <ele-card>
          <template #header>
            <span>选择基础标签模板</span>
          </template>

          <div class="template-selector">
            <el-row :gutter="16">
              <el-col
                v-for="template in labelTemplates"
                :key="template.id"
                :span="6"
              >
                <div
                  class="template-card"
                  :class="{ active: selectedTemplate?.id === template.id }"
                  @click="selectTemplate(template)"
                >
                  <div class="template-preview">
                    <img
                      v-if="template.previewImage"
                      :src="template.previewImage"
                      :alt="template.templateName"
                    />
                    <div v-else class="preview-placeholder">
                      <el-icon><Document /></el-icon>
                    </div>
                  </div>
                  <div class="template-info">
                    <h4>{{ template.templateName }}</h4>
                    <p>{{ template.description || '无描述' }}</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </ele-card>
      </div>

      <!-- 步骤3: 彩签设计 -->
      <div v-if="currentStep === 2" class="step-content">
        <ColorLabelDesigner
          :base-template="selectedTemplate"
          :coin-data="coinData"
          :design-data="currentDesign"
          @save="handleSaveDesign"
          @preview="handlePreviewDesign"
        />
      </div>

      <!-- 步骤4: 预览打印 -->
      <div v-if="currentStep === 3" class="step-content">
        <ColorLabelPreview
          :design-data="currentDesign"
          :coin-data="coinData"
          @print="handlePrint"
          @back-to-design="currentStep = 2"
        />
      </div>

      <!-- 步骤导航按钮 -->
      <div class="step-actions">
        <el-button v-if="currentStep > 0" @click="prevStep">
          上一步
        </el-button>
        <el-button
          v-if="currentStep < 3"
          type="primary"
          @click="nextStep"
          :disabled="!canNextStep"
        >
          下一步
        </el-button>
      </div>

      <!-- 钱币选择器对话框 -->
      <CoinSelectorDialog
        v-model="showCoinSelector"
        @confirm="handleCoinSelection"
      />
    </div>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { Document } from '@element-plus/icons-vue';

  // 导入组件
  import ColorLabelDesigner from './components/ColorLabelDesigner.vue';
  import ColorLabelPreview from './components/ColorLabelPreview.vue';
  import CoinSelectorDialog from './components/CoinSelectorDialog.vue';

  // 导入API
  import {
    getCoinsBySendform,
    getCoinsByDiyCode,
    getLabelTemplates,
    createColorLabelBySendform,
    createColorLabelByDiyCode,
    createColorLabelByCoins,
    saveColorLabelDesign
  } from './api';

  // 响应式数据
  const currentStep = ref(0);
  const loading = ref(false);
  const showCoinSelector = ref(false);

  // 数据源表单
  const dataSourceForm = reactive({
    type: 'sendform',
    sendformNumber: '',
    diyCode: '',
    selectedCoinIds: []
  });

  // 数据
  const coinData = ref([]);
  const selectedCoins = ref([]);
  const labelTemplates = ref([]);
  const selectedTemplate = ref(null);
  const currentDesign = ref(null);

  // 计算属性
  const canNextStep = computed(() => {
    switch (currentStep.value) {
      case 0:
        return coinData.value.length > 0;
      case 1:
        return selectedTemplate.value !== null;
      case 2:
        return currentDesign.value !== null;
      default:
        return true;
    }
  });

  // 方法
  const loadCoinData = async () => {
    loading.value = true;

    try {
      let result = [];

      switch (dataSourceForm.type) {
        case 'sendform':
          if (!dataSourceForm.sendformNumber) {
            throw new Error('请输入送评单号');
          }
          result = await getCoinsBySendform(dataSourceForm.sendformNumber);
          break;

        case 'diycode':
          if (!dataSourceForm.diyCode) {
            throw new Error('请输入送评条码');
          }
          result = await getCoinsByDiyCode(dataSourceForm.diyCode);
          break;

        case 'coins':
          if (selectedCoins.value.length === 0) {
            throw new Error('请选择钱币');
          }
          // 这里需要根据选择的钱币ID获取详细数据
          result = selectedCoins.value;
          break;
      }

      coinData.value = result;
      EleMessage.success(`成功加载 ${result.length} 个钱币数据`);

    } catch (error) {
      EleMessage.error('加载钱币数据失败: ' + error.message);
      coinData.value = [];
    } finally {
      loading.value = false;
    }
  };

  const loadLabelTemplates = async () => {
    try {
      const result = await getLabelTemplates();
      labelTemplates.value = result;
    } catch (error) {
      EleMessage.error('加载标签模板失败: ' + error.message);
    }
  };

  const selectTemplate = (template) => {
    selectedTemplate.value = template;
  };

  const handleCoinSelection = (coins) => {
    selectedCoins.value = coins;
    showCoinSelector.value = false;
  };

  const handleSaveDesign = (designData) => {
    currentDesign.value = designData;
    EleMessage.success('彩签设计已保存');
  };

  const handlePreviewDesign = (designData) => {
    currentDesign.value = designData;
    currentStep.value = 3;
  };

  const handlePrint = (printData) => {
    // 调用打印功能
    EleMessage.success('打印任务已发送');
  };

  const nextStep = () => {
    if (canNextStep.value && currentStep.value < 3) {
      currentStep.value++;

      // 在进入模板选择步骤时加载模板
      if (currentStep.value === 1) {
        loadLabelTemplates();
      }

      // 在进入设计步骤时初始化设计数据
      if (currentStep.value === 2) {
        initializeDesign();
      }
    }
  };

  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  const initializeDesign = async () => {
    try {
      let designData;

      // 根据数据源类型选择对应的创建接口
      if (dataSourceForm.type === 'sendform' && dataSourceForm.sendformNumber) {
        designData = await createColorLabelBySendform(
          dataSourceForm.sendformNumber,
          selectedTemplate.value.id
        );
      } else if (dataSourceForm.type === 'diycode' && dataSourceForm.diyCode) {
        designData = await createColorLabelByDiyCode(
          dataSourceForm.diyCode,
          selectedTemplate.value.id
        );
      } else if (dataSourceForm.selectedCoinIds && dataSourceForm.selectedCoinIds.length > 0) {
        designData = await createColorLabelByCoins(
          dataSourceForm.selectedCoinIds,
          selectedTemplate.value.id
        );
      } else {
        throw new Error('缺少必要的数据源信息');
      }

      currentDesign.value = designData;
    } catch (error) {
      EleMessage.error('初始化设计失败: ' + error.message);
    }
  };

  // 生命周期
  onMounted(() => {
    // 初始化
  });
</script>

<style scoped>
  .color-label-design {
    padding: 20px;
  }

  .steps-nav {
    margin-bottom: 30px;
  }

  .step-content {
    margin-bottom: 30px;
    min-height: 400px;
  }

  .coin-data-preview {
    margin-top: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .coin-data-preview h4 {
    margin-top: 0;
    color: #333;
  }

  .more-data-tip {
    margin: 10px 0 0 0;
    color: #666;
    font-size: 14px;
  }

  .template-selector {
    padding: 20px 0;
  }

  .template-card {
    border: 2px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 16px;
  }

  .template-card:hover {
    border-color: #1677ff;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
  }

  .template-card.active {
    border-color: #1677ff;
    background-color: #f0f8ff;
  }

  .template-preview {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 12px;
  }

  .template-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .preview-placeholder {
    font-size: 32px;
    color: #ccc;
  }

  .template-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #333;
  }

  .template-info p {
    margin: 0;
    font-size: 14px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 20px 0;
    border-top: 1px solid #e8e8e8;
  }
</style>
