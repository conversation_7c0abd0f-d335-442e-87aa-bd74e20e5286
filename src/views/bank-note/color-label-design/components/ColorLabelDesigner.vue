<template>
  <div class="color-label-designer">
    <div class="designer-layout">
      <!-- 左侧工具面板 -->
      <div class="left-panel">
        <ele-card>
          <template #header>
            <span>彩签元素</span>
          </template>

          <div class="element-tools">
            <div class="tool-group">
              <h4>文本元素</h4>
              <div class="tool-item" @click="addTextElement">
                <el-icon><EditPen /></el-icon>
                <span>文本</span>
              </div>
            </div>

            <div class="tool-group">
              <h4>图形元素</h4>
              <div class="tool-item" @click="addImageElement">
                <el-icon><Picture /></el-icon>
                <span>图片</span>
              </div>
              <div class="tool-item" @click="addQRCodeElement">
                <el-icon><Grid /></el-icon>
                <span>二维码</span>
              </div>
            </div>

            <div class="tool-group">
              <h4>形状元素</h4>
              <div class="tool-item" @click="addRectangleElement">
                <el-icon><Minus /></el-icon>
                <span>矩形</span>
              </div>
              <div class="tool-item" @click="addCircleElement">
                <el-icon><CirclePlus /></el-icon>
                <span>圆形</span>
              </div>
            </div>
          </div>
        </ele-card>

        <!-- 图层面板 -->
        <ele-card class="layer-panel">
          <template #header>
            <span>图层管理</span>
          </template>

          <div class="layer-list">
            <!-- 基础模板层（只读） -->
            <div class="layer-item base-layer">
              <el-icon><Document /></el-icon>
              <span>基础模板</span>
              <el-tag size="small" type="info">只读</el-tag>
            </div>

            <!-- 彩签元素层 -->
            <div
              v-for="(element, index) in colorElements"
              :key="element.id"
              class="layer-item"
              :class="{ active: selectedElement?.id === element.id }"
              @click="selectElement(element)"
            >
              <el-icon>
                <EditPen v-if="element.type === 'text'" />
                <Picture v-else-if="element.type === 'image'" />
                <Grid v-else-if="element.type === 'qrcode'" />
                <Minus v-else-if="element.type === 'rectangle'" />
                <CirclePlus v-else-if="element.type === 'circle'" />
              </el-icon>
              <span>{{ element.name || element.type }}</span>
              <div class="layer-actions">
                <el-button size="small" text @click.stop="toggleElementVisibility(element)">
                  <el-icon><View v-if="element.visible" /><Hide v-else /></el-icon>
                </el-button>
                <el-button size="small" text @click.stop="deleteElement(element)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </ele-card>
      </div>

      <!-- 中间设计区域 -->
      <div class="center-panel">
        <div class="design-toolbar">
          <el-button-group size="small">
            <el-button @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
            <el-button @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom">
              <el-icon><FullScreen /></el-icon>
            </el-button>
          </el-button-group>

          <div class="zoom-info">
            缩放: {{ Math.round(zoomLevel * 100) }}%
          </div>

          <el-button-group size="small">
            <el-button @click="undo" :disabled="!canUndo">
              <el-icon><RefreshLeft /></el-icon>
              撤销
            </el-button>
            <el-button @click="redo" :disabled="!canRedo">
              <el-icon><RefreshRight /></el-icon>
              重做
            </el-button>
          </el-button-group>
        </div>

        <!-- 设计画布 -->
        <div class="design-canvas-container">
          <div class="canvas-wrapper" :style="canvasWrapperStyle">
            <!-- 基础模板预览 -->
            <div class="base-template-preview" v-html="baseTemplateHtml"></div>

            <!-- 彩签元素画布 -->
            <canvas
              ref="fabricCanvas"
              :width="canvasWidth"
              :height="canvasHeight"
              class="color-elements-canvas"
            ></canvas>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel">
        <ele-card>
          <template #header>
            <span>属性设置</span>
          </template>

          <div v-if="selectedElement" class="property-panel">
            <!-- 通用属性 -->
            <div class="property-group">
              <h4>基础属性</h4>
              <el-form label-width="60px" size="small">
                <el-form-item label="名称">
                  <el-input v-model="selectedElement.name" />
                </el-form-item>
                <el-form-item label="X坐标">
                  <el-input-number v-model="selectedElement.left" :step="1" />
                </el-form-item>
                <el-form-item label="Y坐标">
                  <el-input-number v-model="selectedElement.top" :step="1" />
                </el-form-item>
                <el-form-item label="宽度">
                  <el-input-number v-model="selectedElement.width" :step="1" />
                </el-form-item>
                <el-form-item label="高度">
                  <el-input-number v-model="selectedElement.height" :step="1" />
                </el-form-item>
              </el-form>
            </div>

            <!-- 文本属性 -->
            <div v-if="selectedElement.type === 'text'" class="property-group">
              <h4>文本属性</h4>
              <el-form label-width="60px" size="small">
                <el-form-item label="内容">
                  <el-input v-model="selectedElement.text" type="textarea" />
                </el-form-item>
                <el-form-item label="字体">
                  <el-select v-model="selectedElement.fontFamily">
                    <el-option label="微软雅黑" value="Microsoft Yahei" />
                    <el-option label="宋体" value="SimSun" />
                    <el-option label="黑体" value="SimHei" />
                  </el-select>
                </el-form-item>
                <el-form-item label="大小">
                  <el-input-number v-model="selectedElement.fontSize" :min="8" :max="72" />
                </el-form-item>
                <el-form-item label="颜色">
                  <el-color-picker v-model="selectedElement.fill" />
                </el-form-item>
              </el-form>
            </div>

            <!-- 数据绑定 -->
            <div class="property-group">
              <h4>数据绑定</h4>
              <el-form label-width="60px" size="small">
                <el-form-item label="字段">
                  <el-select v-model="selectedElement.dataBinding" clearable>
                    <el-option
                      v-for="field in availableFields"
                      :key="field.key"
                      :label="field.label"
                      :value="field.key"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <div v-else class="no-selection">
            <p>请选择一个元素来编辑属性</p>
          </div>
        </ele-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button @click="saveDesign" type="primary">
            保存设计
          </el-button>
          <el-button @click="previewDesign">
            预览效果
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    EditPen, Picture, Grid, Minus, CirclePlus, Document,
    View, Hide, Delete, ZoomIn, ZoomOut, FullScreen,
    RefreshLeft, RefreshRight
  } from '@element-plus/icons-vue';

  const props = defineProps({
    baseTemplate: {
      type: Object,
      required: true
    },
    coinData: {
      type: Array,
      required: true
    },
    designData: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['save', 'preview']);

  // 响应式数据
  const fabricCanvas = ref(null);
  const canvas = ref(null);
  const zoomLevel = ref(1);
  const canvasWidth = ref(800);
  const canvasHeight = ref(600);

  const colorElements = ref([]);
  const selectedElement = ref(null);
  const history = ref([]);
  const historyIndex = ref(-1);

  // 可用的数据字段（基于PjOSendformItem实体）
  const availableFields = ref([
    { key: 'serialNumber', label: '钱币编号' },
    { key: 'coinName1', label: '钱币名称1' },
    { key: 'coinName2', label: '钱币名称2' },
    { key: 'coinName3', label: '钱币名称3' },
    { key: 'gradeScore', label: '评级打分' },
    { key: 'gradeScoreValue', label: '评级分值' },
    { key: 'authenticity', label: '真伪鉴定' },
    { key: 'diyCode', label: '送评条码' },
    { key: 'sendnum', label: '送评单号' },
    { key: 'coinType', label: '钱币类型' },
    { key: 'year', label: '年份' },
    { key: 'faceValue', label: '面值' },
    { key: 'version', label: '版别' },
    { key: 'material', label: '材质' },
    { key: 'coinSize', label: '尺寸' },
    { key: 'coinWeight', label: '重量' },
    { key: 'region', label: '地区' },
    { key: 'catalog', label: '目录' },
    { key: 'bankName', label: '银行名称' },
    { key: 'specialMark', label: '特殊标记' },
    { key: 'remark', label: '备注' }
  ]);

  // 计算属性
  const canvasWrapperStyle = computed(() => ({
    transform: `scale(${zoomLevel.value})`,
    transformOrigin: 'top left'
  }));

  const baseTemplateHtml = computed(() => {
    // 这里应该根据基础模板和钱币数据生成HTML
    // 暂时返回一个示例
    return `<div class="base-template">基础模板预览</div>`;
  });

  const canUndo = computed(() => historyIndex.value > 0);
  const canRedo = computed(() => historyIndex.value < history.value.length - 1);

  // 方法
  const initCanvas = async () => {
    await nextTick();

    // 动态导入 Fabric.js
    const { fabric } = await import('fabric/dist/fabric.min.js');

    canvas.value = new fabric.Canvas(fabricCanvas.value, {
      width: canvasWidth.value,
      height: canvasHeight.value,
      backgroundColor: 'transparent'
    });

    // 监听画布事件
    canvas.value.on('selection:created', handleSelection);
    canvas.value.on('selection:updated', handleSelection);
    canvas.value.on('selection:cleared', () => {
      selectedElement.value = null;
    });
  };

  const handleSelection = (e) => {
    const activeObject = e.selected[0];
    if (activeObject && activeObject.elementData) {
      selectedElement.value = activeObject.elementData;
    }
  };

  const addTextElement = async () => {
    const { fabric } = await import('fabric/dist/fabric.min.js');

    const elementData = {
      id: 'text_' + Date.now(),
      type: 'text',
      name: '文本元素',
      text: '示例文本',
      left: 100,
      top: 100,
      fontSize: 16,
      fontFamily: 'Microsoft Yahei',
      fill: '#000000',
      visible: true,
      dataBinding: null
    };

    const textObject = new fabric.Text(elementData.text, {
      left: elementData.left,
      top: elementData.top,
      fontSize: elementData.fontSize,
      fontFamily: elementData.fontFamily,
      fill: elementData.fill
    });

    textObject.elementData = elementData;
    canvas.value.add(textObject);
    colorElements.value.push(elementData);

    saveToHistory();
  };

  const addImageElement = () => {
    // 实现添加图片元素
    EleMessage.info('图片元素功能开发中...');
  };

  const addQRCodeElement = () => {
    // 实现添加二维码元素
    EleMessage.info('二维码元素功能开发中...');
  };

  const addRectangleElement = async () => {
    const { fabric } = await import('fabric/dist/fabric.min.js');

    const elementData = {
      id: 'rect_' + Date.now(),
      type: 'rectangle',
      name: '矩形',
      left: 150,
      top: 150,
      width: 100,
      height: 60,
      fill: '#ffffff',
      stroke: '#000000',
      strokeWidth: 1,
      visible: true
    };

    const rectObject = new fabric.Rect({
      left: elementData.left,
      top: elementData.top,
      width: elementData.width,
      height: elementData.height,
      fill: elementData.fill,
      stroke: elementData.stroke,
      strokeWidth: elementData.strokeWidth
    });

    rectObject.elementData = elementData;
    canvas.value.add(rectObject);
    colorElements.value.push(elementData);

    saveToHistory();
  };

  const addCircleElement = async () => {
    const { fabric } = await import('fabric/dist/fabric.min.js');

    const elementData = {
      id: 'circle_' + Date.now(),
      type: 'circle',
      name: '圆形',
      left: 200,
      top: 200,
      radius: 30,
      fill: '#ffffff',
      stroke: '#000000',
      strokeWidth: 1,
      visible: true
    };

    const circleObject = new fabric.Circle({
      left: elementData.left,
      top: elementData.top,
      radius: elementData.radius,
      fill: elementData.fill,
      stroke: elementData.stroke,
      strokeWidth: elementData.strokeWidth
    });

    circleObject.elementData = elementData;
    canvas.value.add(circleObject);
    colorElements.value.push(elementData);

    saveToHistory();
  };

  const selectElement = (element) => {
    selectedElement.value = element;

    // 在画布中选中对应的对象
    const objects = canvas.value.getObjects();
    const targetObject = objects.find(obj => obj.elementData?.id === element.id);
    if (targetObject) {
      canvas.value.setActiveObject(targetObject);
      canvas.value.renderAll();
    }
  };

  const toggleElementVisibility = (element) => {
    element.visible = !element.visible;

    // 更新画布中的对象
    const objects = canvas.value.getObjects();
    const targetObject = objects.find(obj => obj.elementData?.id === element.id);
    if (targetObject) {
      targetObject.set({ visible: element.visible });
      canvas.value.renderAll();
    }
  };

  const deleteElement = (element) => {
    const index = colorElements.value.findIndex(el => el.id === element.id);
    if (index > -1) {
      colorElements.value.splice(index, 1);
    }

    // 从画布中移除对象
    const objects = canvas.value.getObjects();
    const targetObject = objects.find(obj => obj.elementData?.id === element.id);
    if (targetObject) {
      canvas.value.remove(targetObject);
    }

    if (selectedElement.value?.id === element.id) {
      selectedElement.value = null;
    }

    saveToHistory();
  };

  const zoomIn = () => {
    zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3);
  };

  const zoomOut = () => {
    zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.1);
  };

  const resetZoom = () => {
    zoomLevel.value = 1;
  };

  const saveToHistory = () => {
    const state = {
      elements: JSON.parse(JSON.stringify(colorElements.value)),
      canvasData: canvas.value.toJSON()
    };

    history.value = history.value.slice(0, historyIndex.value + 1);
    history.value.push(state);
    historyIndex.value = history.value.length - 1;
  };

  const undo = () => {
    if (canUndo.value) {
      historyIndex.value--;
      restoreFromHistory();
    }
  };

  const redo = () => {
    if (canRedo.value) {
      historyIndex.value++;
      restoreFromHistory();
    }
  };

  const restoreFromHistory = () => {
    const state = history.value[historyIndex.value];
    colorElements.value = JSON.parse(JSON.stringify(state.elements));
    canvas.value.loadFromJSON(state.canvasData, () => {
      canvas.value.renderAll();
    });
  };

  const saveDesign = () => {
    const designData = {
      baseTemplateId: props.baseTemplate.id,
      colorElements: colorElements.value,
      canvasData: canvas.value.toJSON(),
      coinData: props.coinData
    };

    emit('save', designData);
  };

  const previewDesign = () => {
    const designData = {
      baseTemplateId: props.baseTemplate.id,
      colorElements: colorElements.value,
      canvasData: canvas.value.toJSON(),
      coinData: props.coinData
    };

    emit('preview', designData);
  };

  // 监听属性变化
  watch(selectedElement, (newElement) => {
    if (newElement && canvas.value) {
      // 同步属性变化到画布对象
      const objects = canvas.value.getObjects();
      const targetObject = objects.find(obj => obj.elementData?.id === newElement.id);
      if (targetObject) {
        targetObject.set(newElement);
        canvas.value.renderAll();
      }
    }
  }, { deep: true });

  // 生命周期
  onMounted(() => {
    initCanvas();
  });
</script>

<style scoped>
  .color-label-designer {
    height: 100%;
  }

  .designer-layout {
    display: flex;
    height: 600px;
    gap: 16px;
  }

  .left-panel {
    width: 250px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .center-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .right-panel {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .element-tools {
    padding: 16px 0;
  }

  .tool-group {
    margin-bottom: 20px;
  }

  .tool-group h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
  }

  .tool-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .tool-item:hover {
    background-color: #f5f5f5;
  }

  .layer-panel {
    flex: 1;
  }

  .layer-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .layer-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .layer-item:hover {
    background-color: #f5f5f5;
  }

  .layer-item.active {
    background-color: #e6f7ff;
    color: #1677ff;
  }

  .layer-item.base-layer {
    background-color: #f0f0f0;
    cursor: default;
  }

  .layer-actions {
    margin-left: auto;
    display: flex;
    gap: 4px;
  }

  .design-toolbar {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #fafafa;
  }

  .zoom-info {
    font-size: 14px;
    color: #666;
  }

  .design-canvas-container {
    flex: 1;
    overflow: auto;
    background-color: #f5f5f5;
    position: relative;
  }

  .canvas-wrapper {
    position: relative;
    margin: 20px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .base-template-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  .color-elements-canvas {
    position: relative;
    z-index: 2;
  }

  .property-panel {
    padding: 16px 0;
  }

  .property-group {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .property-group:last-child {
    border-bottom: none;
  }

  .property-group h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #333;
  }

  .no-selection {
    padding: 40px 20px;
    text-align: center;
    color: #999;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
</style>
