<template>
  <el-dialog
    v-model="visible"
    title="选择钱币"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="coin-selector-dialog">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="送评单号">
            <el-input
              v-model="searchForm.sendformNumber"
              placeholder="请输入送评单号"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="钱币编号">
            <el-input
              v-model="searchForm.serialNumber"
              placeholder="请输入钱币编号"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="钱币名称">
            <el-input
              v-model="searchForm.coinName1"
              placeholder="请输入钱币名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 钱币列表 -->
      <div class="coin-list">
        <el-table
          ref="tableRef"
          :data="coinList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="serialNumber" label="钱币编号" width="120" />
          <el-table-column prop="coinName1" label="钱币名称" min-width="200" />
          <el-table-column prop="gradeScore" label="评级打分" width="100" />
          <el-table-column prop="coinType" label="钱币类型" width="100" />
          <el-table-column prop="diyCode" label="送评条码" width="150" />
          <el-table-column prop="sendnum" label="送评单号" width="150" />
          <el-table-column prop="authenticity" label="真伪" width="80">
            <template #default="{ row }">
              <el-tag
                :type="row.authenticity === '真' ? 'success' : row.authenticity === '假' ? 'danger' : 'info'"
                size="small"
              >
                {{ row.authenticity || '待鉴定' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSearch"
            @current-change="handleSearch"
          />
        </div>
      </div>

      <!-- 已选择的钱币 -->
      <div class="selected-coins" v-if="selectedCoins.length > 0">
        <h4>已选择的钱币 ({{ selectedCoins.length }})</h4>
        <div class="selected-list">
          <el-tag
            v-for="coin in selectedCoins"
            :key="coin.id"
            closable
            @close="removeCoin(coin)"
            class="selected-tag"
          >
            {{ coin.serialNumber }} - {{ coin.coinName1 }}
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <span class="selected-count">
          已选择 {{ selectedCoins.length }} 个钱币
        </span>
        <div class="footer-buttons">
          <el-button @click="handleCancel">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirm"
            :disabled="selectedCoins.length === 0"
          >
            确定选择
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { Search, Refresh } from '@element-plus/icons-vue';

  // 导入API（这里使用模拟数据，实际项目中应该导入真实API）
  // import { searchCoins } from '../api';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    maxSelection: {
      type: Number,
      default: 0 // 0表示无限制
    }
  });

  const emit = defineEmits(['update:modelValue', 'confirm']);

  // 响应式数据
  const loading = ref(false);
  const tableRef = ref(null);
  const coinList = ref([]);
  const selectedCoins = ref([]);

  const searchForm = reactive({
    sendformNumber: '',
    serialNumber: '',
    coinName1: ''
  });

  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  });

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  // 方法
  const handleSearch = async () => {
    try {
      loading.value = true;

      // 模拟API调用
      const mockData = generateMockData();
      coinList.value = mockData.records;
      pagination.total = mockData.total;

      // 实际项目中应该使用真实API
      // const result = await searchCoins({
      //   ...searchForm,
      //   current: pagination.current,
      //   size: pagination.size
      // });
      // coinList.value = result.records;
      // pagination.total = result.total;

    } catch (error) {
      console.error('搜索钱币失败:', error);
      EleMessage.error('搜索钱币失败: ' + error.message);
    } finally {
      loading.value = false;
    }
  };

  const handleReset = () => {
    Object.keys(searchForm).forEach(key => {
      searchForm[key] = '';
    });
    pagination.current = 1;
    handleSearch();
  };

  const handleSelectionChange = (selection) => {
    if (props.maxSelection > 0 && selection.length > props.maxSelection) {
      EleMessage.warning(`最多只能选择 ${props.maxSelection} 个钱币`);
      // 保持之前的选择
      return;
    }
    selectedCoins.value = selection;
  };

  const removeCoin = (coin) => {
    const index = selectedCoins.value.findIndex(item => item.id === coin.id);
    if (index > -1) {
      selectedCoins.value.splice(index, 1);
      // 同步更新表格选择状态
      if (tableRef.value) {
        tableRef.value.toggleRowSelection(coin, false);
      }
    }
  };

  const handleConfirm = () => {
    if (selectedCoins.value.length === 0) {
      EleMessage.warning('请至少选择一个钱币');
      return;
    }

    emit('confirm', [...selectedCoins.value]);
    handleCancel();
  };

  const handleCancel = () => {
    visible.value = false;
    // 清空选择
    selectedCoins.value = [];
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockCoins = [];
    for (let i = 1; i <= 50; i++) {
      mockCoins.push({
        id: `coin_${i}`,
        serialNumber: `SN${String(i).padStart(6, '0')}`,
        coinName1: `测试钱币${i}`,
        gradeScore: ['MS70', 'MS69', 'MS68', 'AU58'][Math.floor(Math.random() * 4)],
        coinType: ['纸币', '机制币', '古钱币', '银锭'][Math.floor(Math.random() * 4)],
        diyCode: `DIY${String(i).padStart(8, '0')}`,
        sendnum: `SF${String(Math.floor(i / 10) + 1).padStart(6, '0')}`,
        authenticity: ['真', '假', '待鉴定'][Math.floor(Math.random() * 3)],
        year: `${1900 + Math.floor(Math.random() * 120)}`,
        faceValue: ['1元', '5元', '10元', '100元'][Math.floor(Math.random() * 4)]
      });
    }

    // 根据搜索条件过滤
    let filteredCoins = mockCoins;
    if (searchForm.sendformNumber) {
      filteredCoins = filteredCoins.filter(coin =>
        coin.sendnum.includes(searchForm.sendformNumber)
      );
    }
    if (searchForm.serialNumber) {
      filteredCoins = filteredCoins.filter(coin =>
        coin.serialNumber.includes(searchForm.serialNumber)
      );
    }
    if (searchForm.coinName1) {
      filteredCoins = filteredCoins.filter(coin =>
        coin.coinName1.includes(searchForm.coinName1)
      );
    }

    // 分页
    const start = (pagination.current - 1) * pagination.size;
    const end = start + pagination.size;

    return {
      records: filteredCoins.slice(start, end),
      total: filteredCoins.length
    };
  };

  // 监听对话框打开
  watch(visible, (newVal) => {
    if (newVal) {
      handleSearch();
    }
  });
</script>

<style scoped>
  .coin-selector-dialog {
    max-height: 70vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .coin-list {
    flex: 1;
    overflow: hidden;
  }

  .pagination-wrapper {
    margin-top: 16px;
    text-align: right;
  }

  .selected-coins {
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
  }

  .selected-coins h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
  }

  .selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .selected-tag {
    margin: 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .selected-count {
    color: #666;
    font-size: 14px;
  }

  .footer-buttons {
    display: flex;
    gap: 8px;
  }
</style>
