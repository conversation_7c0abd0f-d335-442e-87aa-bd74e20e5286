<template>
  <div class="color-label-preview">
    <ele-card>
      <template #header>
        <div class="preview-header">
          <span>彩签预览</span>
          <div class="preview-actions">
            <el-button @click="handleBackToDesign">
              返回设计
            </el-button>
            <el-button type="primary" @click="handlePrint" :loading="printing">
              <el-icon><Printer /></el-icon>
              打印彩签
            </el-button>
          </div>
        </div>
      </template>

      <div class="preview-content">
        <!-- 预览工具栏 -->
        <div class="preview-toolbar">
          <div class="toolbar-left">
            <span class="preview-info">
              共 {{ previewItems.length }} 个彩签
            </span>
          </div>
          <div class="toolbar-right">
            <el-button-group>
              <el-button
                :icon="ZoomOut"
                @click="zoomOut"
                :disabled="zoomLevel <= 0.5"
                size="small"
              />
              <el-button size="small" disabled>
                {{ Math.round(zoomLevel * 100) }}%
              </el-button>
              <el-button
                :icon="ZoomIn"
                @click="zoomIn"
                :disabled="zoomLevel >= 2"
                size="small"
              />
            </el-button-group>
            <el-button @click="refreshPreview" :icon="Refresh" size="small">
              刷新
            </el-button>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-area" :style="previewAreaStyle">
          <div
            v-for="(item, index) in previewItems"
            :key="item.coinId || index"
            class="preview-item"
            :style="previewItemStyle"
          >
            <div class="preview-label">
              <div class="coin-info">
                <h4>{{ item.coinData.coinName1 || '钱币名称' }}</h4>
                <p>编号: {{ item.coinData.serialNumber || 'N/A' }}</p>
                <p>等级: {{ item.coinData.gradeScore || 'N/A' }}</p>
                <p>送评单号: {{ item.coinData.sendnum || 'N/A' }}</p>
              </div>

              <!-- 渲染预览内容 -->
              <div
                class="label-content"
                v-html="item.renderedHtml"
                :style="labelContentStyle"
              />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="previewItems.length === 0" class="empty-preview">
          <el-empty description="暂无预览数据">
            <el-button type="primary" @click="handleBackToDesign">
              返回设计
            </el-button>
          </el-empty>
        </div>
      </div>
    </ele-card>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    Printer,
    ZoomIn,
    ZoomOut,
    Refresh
  } from '@element-plus/icons-vue';

  // 导入API
  import { previewColorLabelDesign } from '../api';

  const props = defineProps({
    designData: {
      type: Object,
      required: true
    },
    coinData: {
      type: Array,
      required: true
    }
  });

  const emit = defineEmits(['print', 'back-to-design']);

  // 响应式数据
  const loading = ref(false);
  const printing = ref(false);
  const zoomLevel = ref(1);
  const previewItems = ref([]);

  // 计算属性
  const previewAreaStyle = computed(() => ({
    transform: `scale(${zoomLevel.value})`,
    transformOrigin: 'top left',
    transition: 'transform 0.3s ease'
  }));

  const previewItemStyle = computed(() => ({
    marginBottom: '20px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    padding: '10px',
    backgroundColor: '#fff',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  }));

  const labelContentStyle = computed(() => ({
    minHeight: '100px',
    border: '1px dashed #ccc',
    padding: '10px',
    marginTop: '10px',
    backgroundColor: '#fafafa'
  }));

  // 方法
  const zoomIn = () => {
    if (zoomLevel.value < 2) {
      zoomLevel.value = Math.min(2, zoomLevel.value + 0.1);
    }
  };

  const zoomOut = () => {
    if (zoomLevel.value > 0.5) {
      zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1);
    }
  };

  const refreshPreview = async () => {
    await loadPreviewData();
  };

  const handleBackToDesign = () => {
    emit('back-to-design');
  };

  const handlePrint = async () => {
    try {
      printing.value = true;

      const printData = {
        designData: props.designData,
        previewItems: previewItems.value
      };

      emit('print', printData);
      EleMessage.success('打印任务已发送');

    } catch (error) {
      console.error('打印失败:', error);
      EleMessage.error('打印失败: ' + error.message);
    } finally {
      printing.value = false;
    }
  };

  const loadPreviewData = async () => {
    try {
      loading.value = true;

      const result = await previewColorLabelDesign(props.designData);
      previewItems.value = result.previewItems || [];

    } catch (error) {
      console.error('加载预览数据失败:', error);
      EleMessage.error('加载预览数据失败: ' + error.message);

      // 使用默认预览数据
      previewItems.value = generateDefaultPreviewItems();
    } finally {
      loading.value = false;
    }
  };

  const generateDefaultPreviewItems = () => {
    return props.coinData.map((coin, index) => ({
      coinId: coin.id || `coin_${index}`,
      coinData: {
        coinName1: coin.coinName1 || '钱币名称',
        serialNumber: coin.serialNumber || `SN${String(index + 1).padStart(3, '0')}`,
        gradeScore: coin.gradeScore || 'MS70',
        sendnum: coin.sendnum || '送评单号',
        diyCode: coin.diyCode || `DIY${String(index + 1).padStart(6, '0')}`,
        coinType: coin.coinType || '钱币类型',
        year: coin.year || '年份',
        faceValue: coin.faceValue || '面值',
        authenticity: coin.authenticity || '真伪',
        remark: coin.remark || ''
      },
      renderedHtml: `
        <div style="padding: 10px; font-family: Arial, sans-serif;">
          <h3 style="margin: 0 0 10px 0; color: #333;">${coin.coinName1 || '钱币名称'}</h3>
          <p style="margin: 5px 0; color: #666;">编号: ${coin.serialNumber || 'N/A'}</p>
          <p style="margin: 5px 0; color: #666;">等级: ${coin.gradeScore || 'N/A'}</p>
          <p style="margin: 5px 0; color: #666;">送评单号: ${coin.sendnum || 'N/A'}</p>
          <p style="margin: 5px 0; color: #666;">类型: ${coin.coinType || 'N/A'}</p>
          <p style="margin: 5px 0; color: #666;">年份: ${coin.year || 'N/A'}</p>
          <div style="margin-top: 10px; padding: 5px; background: #f0f0f0; border-radius: 3px;">
            <small>条码: ${coin.diyCode || 'N/A'}</small>
          </div>
        </div>
      `
    }));
  };

  // 监听设计数据变化
  watch(() => props.designData, () => {
    if (props.designData) {
      loadPreviewData();
    }
  }, { immediate: true });

  // 生命周期
  onMounted(() => {
    if (props.designData) {
      loadPreviewData();
    }
  });
</script>

<style scoped>
  .color-label-preview {
    height: 100%;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .preview-actions {
    display: flex;
    gap: 8px;
  }

  .preview-content {
    height: calc(100vh - 200px);
    overflow: auto;
  }

  .preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .preview-info {
    color: #666;
    font-size: 14px;
  }

  .preview-area {
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
    min-height: 400px;
  }

  .preview-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
  }

  .coin-info {
    padding: 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
  }

  .coin-info h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
  }

  .coin-info p {
    margin: 4px 0;
    color: #666;
    font-size: 14px;
  }

  .label-content {
    padding: 15px;
  }

  .empty-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }
</style>
