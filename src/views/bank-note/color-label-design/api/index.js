import request from '@/utils/request';

/**
 * 彩签设计API接口
 * 基于现有的批量打印功能扩展
 * <AUTHOR>
 * @date 2025-01-15
 */

const API_BASE = '/api/color-label-design';

/**
 * 根据送评单号获取钱币信息
 * @param {string} sendformNumber 送评单号
 * @returns {Promise} 钱币信息列表
 */
export function getCoinsBySendform(sendformNumber) {
  return request.get(`/api/sendform/${sendformNumber}/coins`);
}

/**
 * 根据送评条码获取钱币信息
 * @param {string} diyCode 送评条码
 * @returns {Promise} 钱币信息
 */
export function getCoinsByDiyCode(diyCode) {
  return request.get(`/api/sendform/coin/${diyCode}`);
}

/**
 * 获取标签模板列表
 * @returns {Promise} 标签模板列表
 */
export function getLabelTemplates() {
  return request.get('/api/label-templates');
}

/**
 * 根据ID获取标签模板详情
 * @param {string} templateId 模板ID
 * @returns {Promise} 模板详情
 */
export function getLabelTemplate(templateId) {
  return request.get(`/api/label-templates/${templateId}`);
}

/**
 * 创建彩签设计
 * @param {Object} data 创建参数
 * @returns {Promise} 设计数据
 */
export function createColorLabelDesign(data) {
  return request.post(`${API_BASE}/create`, data);
}

/**
 * 保存彩签设计
 * @param {Object} data 设计数据
 * @returns {Promise} 保存结果
 */
export function saveColorLabelDesign(data) {
  return request.post(`${API_BASE}/save`, data);
}

/**
 * 更新彩签设计
 * @param {string} id 设计ID
 * @param {Object} data 设计数据
 * @returns {Promise} 更新结果
 */
export function updateColorLabelDesign(id, data) {
  return request.put(`${API_BASE}/${id}`, data);
}

/**
 * 获取彩签设计详情
 * @param {string} id 设计ID
 * @returns {Promise} 设计详情
 */
export function getColorLabelDesign(id) {
  return request.get(`${API_BASE}/${id}`);
}

/**
 * 删除彩签设计
 * @param {string} id 设计ID
 * @returns {Promise} 删除结果
 */
export function deleteColorLabelDesign(id) {
  return request.delete(`${API_BASE}/${id}`);
}

/**
 * 预览彩签设计
 * @param {Object} data 设计数据
 * @returns {Promise} 预览数据
 */
export function previewColorLabelDesign(data) {
  return request.post(`${API_BASE}/preview`, data);
}

/**
 * 生成彩签批量打印数据
 * @param {string} designId 设计ID
 * @returns {Promise} 打印数据
 */
export function generateColorLabelPrintData(designId) {
  return request.post(`${API_BASE}/${designId}/generate-print-data`);
}

/**
 * 获取彩签设计历史记录
 * @param {string} sendformNumber 送评单号
 * @returns {Promise} 历史记录列表
 */
export function getColorLabelDesignHistory(sendformNumber) {
  return request.get(`${API_BASE}/history`, {
    params: { sendformNumber }
  });
}

/**
 * 复制彩签设计
 * @param {string} sourceId 源设计ID
 * @param {string} newName 新设计名称
 * @returns {Promise} 复制结果
 */
export function copyColorLabelDesign(sourceId, newName) {
  return request.post(`${API_BASE}/${sourceId}/copy`, { newName });
}

/**
 * 获取彩签设计模板列表
 * @returns {Promise} 设计模板列表
 */
export function getColorLabelDesignTemplates() {
  return request.get(`${API_BASE}/templates`);
}

/**
 * 应用彩签设计模板
 * @param {string} templateId 模板ID
 * @param {Array} coinIds 钱币ID列表
 * @returns {Promise} 应用结果
 */
export function applyColorLabelDesignTemplate(templateId, coinIds) {
  return request.post(`${API_BASE}/templates/${templateId}/apply`, { coinIds });
}

/**
 * 获取可用的彩签元素类型
 * @returns {Promise} 元素类型列表
 */
export function getAvailableColorLabelElements() {
  return request.get(`${API_BASE}/elements`);
}

/**
 * 验证彩签设计配置
 * @param {Object} config 设计配置
 * @returns {Promise} 验证结果
 */
export function validateColorLabelDesign(config) {
  return request.post(`${API_BASE}/validate`, config);
}

/**
 * 上传彩签图片素材
 * @param {FormData} formData 文件数据
 * @returns {Promise} 上传结果
 */
export function uploadColorLabelImage(formData) {
  return request.post(`${API_BASE}/upload-image`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 获取彩签设计统计信息
 * @param {string} designId 设计ID
 * @returns {Promise} 统计信息
 */
export function getColorLabelDesignStatistics(designId) {
  return request.get(`${API_BASE}/${designId}/statistics`);
}

/**
 * 导出彩签设计
 * @param {string} designId 设计ID
 * @returns {Promise} 导出数据
 */
export function exportColorLabelDesign(designId) {
  return request.get(`${API_BASE}/${designId}/export`);
}

/**
 * 导入彩签设计
 * @param {FormData} formData 设计文件
 * @returns {Promise} 导入结果
 */
export function importColorLabelDesign(formData) {
  return request.post(`${API_BASE}/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 批量应用彩签设计
 * @param {string} designId 设计ID
 * @param {Array} sendformNumbers 送评单号列表
 * @returns {Promise} 批量应用结果
 */
export function batchApplyColorLabelDesign(designId, sendformNumbers) {
  return request.post(`${API_BASE}/${designId}/batch-apply`, { sendformNumbers });
}

/**
 * 获取彩签设计使用记录
 * @param {string} designId 设计ID
 * @returns {Promise} 使用记录列表
 */
export function getColorLabelDesignUsageHistory(designId) {
  return request.get(`${API_BASE}/${designId}/usage-history`);
}
