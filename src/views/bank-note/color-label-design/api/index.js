import request from '@/utils/request';

/**
 * 彩签设计API接口
 * 基于现有的批量打印功能扩展
 * <AUTHOR>
 * @date 2025-01-15
 */

const API_BASE = '/api/color-label-design';

/**
 * 根据送评单号获取钱币信息
 * @param {string} sendformNumber 送评单号
 * @returns {Promise} 钱币信息列表
 */
export function getCoinsBySendform(sendformNumber) {
  return request.get(`/api/sendform/${sendformNumber}/coins`);
}

/**
 * 根据送评条码获取钱币信息
 * @param {string} diyCode 送评条码
 * @returns {Promise} 钱币信息
 */
export function getCoinsByDiyCode(diyCode) {
  return request.get(`/api/sendform/coin/${diyCode}`);
}

/**
 * 获取标签模板列表
 * @returns {Promise} 标签模板列表
 */
export function getLabelTemplates() {
  return request.get('/api/label-templates');
}

/**
 * 根据ID获取标签模板详情
 * @param {string} templateId 模板ID
 * @returns {Promise} 模板详情
 */
export function getLabelTemplate(templateId) {
  return request.get(`/api/label-templates/${templateId}`);
}

/**
 * 根据送评单号创建彩签设计
 * @param {string} sendformNumber 送评单号
 * @param {string} templateId 基础模板ID
 * @returns {Promise} 设计数据
 */
export function createColorLabelBySendform(sendformNumber, templateId) {
  return request.post(`${API_BASE}/create-by-sendform`, { sendformNumber, templateId });
}

/**
 * 根据送评条码创建彩签设计
 * @param {string} diyCode 送评条码
 * @param {string} templateId 基础模板ID
 * @returns {Promise} 设计数据
 */
export function createColorLabelByDiyCode(diyCode, templateId) {
  return request.post(`${API_BASE}/create-by-diycode`, { diyCode, templateId });
}

/**
 * 根据钱币ID列表创建彩签设计
 * @param {Array} coinIds 钱币ID列表
 * @param {string} templateId 基础模板ID
 * @returns {Promise} 设计数据
 */
export function createColorLabelByCoins(coinIds, templateId) {
  return request.post(`${API_BASE}/create-by-coins`, { coinIds, templateId });
}

/**
 * 创建彩签设计（通用方法，根据参数自动选择具体的创建方式）
 * @param {Object} data 创建参数
 * @returns {Promise} 设计数据
 */
export function createColorLabelDesign(data) {
  if (data.sendformNumber && data.templateId) {
    return createColorLabelBySendform(data.sendformNumber, data.templateId);
  } else if (data.diyCode && data.templateId) {
    return createColorLabelByDiyCode(data.diyCode, data.templateId);
  } else if (data.coinIds && data.templateId) {
    return createColorLabelByCoins(data.coinIds, data.templateId);
  } else {
    throw new Error('缺少必要的创建参数');
  }
}

/**
 * 保存彩签设计
 * @param {Object} data 设计数据
 * @returns {Promise} 保存结果
 */
export function saveColorLabelDesign(data) {
  return request.post(`${API_BASE}/save`, data);
}

/**
 * 更新彩签设计（注意：后端暂未实现此接口，使用保存接口代替）
 * @param {string} id 设计ID
 * @param {Object} data 设计数据
 * @returns {Promise} 更新结果
 */
export function updateColorLabelDesign(id, data) {
  // 后端暂未实现PUT接口，使用保存接口代替
  return saveColorLabelDesign({ ...data, id });
}

/**
 * 获取彩签设计详情
 * @param {string} id 设计ID
 * @returns {Promise} 设计详情
 */
export function getColorLabelDesign(id) {
  return request.get(`${API_BASE}/${id}`);
}

/**
 * 删除彩签设计
 * @param {string} id 设计ID
 * @returns {Promise} 删除结果
 */
export function deleteColorLabelDesign(id) {
  return request.delete(`${API_BASE}/${id}`);
}

/**
 * 预览彩签设计
 * @param {Object} data 设计数据
 * @returns {Promise} 预览数据
 */
export function previewColorLabelDesign(data) {
  return request.post(`${API_BASE}/preview`, data);
}

/**
 * 生成彩签批量打印数据
 * @param {string} designId 设计ID
 * @returns {Promise} 打印数据
 */
export function generateColorLabelPrintData(designId) {
  return request.post(`${API_BASE}/${designId}/generate-print-data`);
}

/**
 * 获取彩签设计历史记录
 * @param {string} sendformNumber 送评单号
 * @returns {Promise} 历史记录列表
 */
export function getColorLabelDesignHistory(sendformNumber) {
  return request.get(`${API_BASE}/history`, {
    params: { sendformNumber }
  });
}

/**
 * 复制彩签设计
 * @param {string} sourceId 源设计ID
 * @param {string} newName 新设计名称
 * @returns {Promise} 复制结果
 */
export function copyColorLabelDesign(sourceId, newName) {
  return request.post(`${API_BASE}/${sourceId}/copy`, { newName });
}

/**
 * 获取彩签设计模板列表
 * @returns {Promise} 设计模板列表
 */
export function getColorLabelDesignTemplates() {
  return request.get(`${API_BASE}/templates`);
}

/**
 * 应用彩签设计模板
 * @param {string} templateId 模板ID
 * @param {Array} coinIds 钱币ID列表
 * @returns {Promise} 应用结果
 */
export function applyColorLabelDesignTemplate(templateId, coinIds) {
  return request.post(`${API_BASE}/templates/${templateId}/apply`, { coinIds });
}

/**
 * 获取可用的彩签元素类型
 * @returns {Promise} 元素类型列表
 */
export function getAvailableColorLabelElements() {
  return request.get(`${API_BASE}/elements`);
}

/**
 * 验证彩签设计配置
 * @param {Object} config 设计配置
 * @returns {Promise} 验证结果
 */
export function validateColorLabelDesign(config) {
  return request.post(`${API_BASE}/validate`, config);
}

/**
 * 上传彩签图片素材
 * @param {FormData} formData 文件数据
 * @returns {Promise} 上传结果
 */
export function uploadColorLabelImage(formData) {
  return request.post(`${API_BASE}/upload-image`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 以下接口后端暂未实现，预留给未来扩展使用

/**
 * 获取彩签设计统计信息（后端暂未实现）
 * @param {string} designId 设计ID
 * @returns {Promise} 统计信息
 */
export function getColorLabelDesignStatistics(designId) {
  console.warn('getColorLabelDesignStatistics: 后端接口暂未实现');
  return Promise.resolve({
    designId,
    usageCount: 0,
    lastUsedTime: null,
    createdTime: new Date().toISOString()
  });
}

/**
 * 导出彩签设计（后端暂未实现）
 * @param {string} designId 设计ID
 * @returns {Promise} 导出数据
 */
export function exportColorLabelDesign(designId) {
  console.warn('exportColorLabelDesign: 后端接口暂未实现');
  return Promise.reject(new Error('导出功能暂未实现'));
}

/**
 * 导入彩签设计（后端暂未实现）
 * @param {FormData} formData 设计文件
 * @returns {Promise} 导入结果
 */
export function importColorLabelDesign(formData) {
  console.warn('importColorLabelDesign: 后端接口暂未实现');
  return Promise.reject(new Error('导入功能暂未实现'));
}

/**
 * 批量应用彩签设计（后端暂未实现）
 * @param {string} designId 设计ID
 * @param {Array} sendformNumbers 送评单号列表
 * @returns {Promise} 批量应用结果
 */
export function batchApplyColorLabelDesign(designId, sendformNumbers) {
  console.warn('batchApplyColorLabelDesign: 后端接口暂未实现');
  return Promise.reject(new Error('批量应用功能暂未实现'));
}

/**
 * 获取彩签设计使用记录（后端暂未实现）
 * @param {string} designId 设计ID
 * @returns {Promise} 使用记录列表
 */
export function getColorLabelDesignUsageHistory(designId) {
  console.warn('getColorLabelDesignUsageHistory: 后端接口暂未实现');
  return Promise.resolve([]);
}
