# 钱币字段映射修复说明

## 问题描述

前端代码中使用的钱币字段名称与后端 `PjOSendformItem` 实体类的实际字段不匹配，导致数据显示错误。

## 修复内容

### 🔧 字段映射对照表

| 前端原字段名 | 后端实际字段名 | 字段说明 | 修复状态 |
|-------------|---------------|----------|----------|
| `itemName` | `coinName1` | 钱币名称1 | ✅ 已修复 |
| `gradeLevel` | `gradeScore` | 评级打分 | ✅ 已修复 |
| `customerName` | `sendnum` | 送评单号（客户信息） | ✅ 已修复 |
| `sendformNumber` | `sendnum` | 送评单号 | ✅ 已修复 |

### 📋 完整的 PjOSendformItem 字段列表

#### 基础信息字段
- `id` - 主键ID
- `sendnum` - 送评单号
- `seqno` - 序号
- `coinType` - 钱币类型
- `serialNumber` - 钱币编号（冠号）
- `diyCode` - 送评条码

#### 钱币名称字段
- `coinName1` - 钱币名称1
- `coinName2` - 钱币名称2
- `coinName3` - 钱币名称3

#### 钱币属性字段
- `year` - 年份
- `faceValue` - 面值
- `faceVal` - 面值（备用）
- `version` - 版别
- `material` - 材质
- `coinSize` - 尺寸
- `coinWeight` - 重量
- `quantity` - 数量

#### 评级相关字段
- `authenticity` - 真伪鉴定
- `gradeScore` - 评级打分
- `gradeScoreValue` - 评级品相分值
- `scoreRemarks` - 评分备注
- `specialMark` - 特殊标记

#### 地区和分类字段
- `region` - 地区
- `catalog` - 目录
- `bankName` - 银行名称
- `belongName` - 所属名称
- `weightName` - 重量名称
- `yearInfo` - 年代
- `taxType` - 税种
- `rank` - 等级

#### 价格相关字段
- `gradeFee` - 评级费
- `standardPrice` - 标准价
- `internationalPrice` - 国际价
- `fee` - 费用
- `discount` - 折扣
- `boxFee` - 盒子费
- `urgentFee` - 加急费

#### 备注字段
- `remark` - 备注
- `internalNote` - 对内备注
- `externalNote` - 对外备注
- `inspectionNote` - 验货标注
- `specialLabel` - 特殊标签

#### 其他字段
- `boxType` - 盒子类型
- `coinImages` - 钱币图片
- `createTime` - 创建时间

## 修复的文件

### 1. ColorLabelPreview.vue
**修复内容：**
- 钱币信息显示字段更新
- 预览数据生成逻辑调整
- 默认预览项字段映射修正

**主要更改：**
```javascript
// 修复前
item.coinData.itemName || '钱币名称'
item.coinData.gradeLevel || 'N/A'

// 修复后
item.coinData.coinName1 || '钱币名称'
item.coinData.gradeScore || 'N/A'
item.coinData.sendnum || 'N/A'
```

### 2. ColorLabelDesigner.vue
**修复内容：**
- 可用数据字段列表完全重构
- 基于 PjOSendformItem 实体字段定义
- 增加了更多可用字段选项

**主要更改：**
```javascript
// 修复前
const availableFields = ref([
  { key: 'serialNumber', label: '钱币编号' },
  { key: 'itemName', label: '钱币名称' },
  { key: 'gradeLevel', label: '品相等级' },
  { key: 'customerName', label: '客户名称' },
  { key: 'diyCode', label: '送评条码' }
]);

// 修复后
const availableFields = ref([
  { key: 'serialNumber', label: '钱币编号' },
  { key: 'coinName1', label: '钱币名称1' },
  { key: 'coinName2', label: '钱币名称2' },
  { key: 'coinName3', label: '钱币名称3' },
  { key: 'gradeScore', label: '评级打分' },
  { key: 'gradeScoreValue', label: '评级分值' },
  { key: 'authenticity', label: '真伪鉴定' },
  // ... 更多字段
]);
```

### 3. CoinSelectorDialog.vue
**修复内容：**
- 表格列定义更新
- 搜索表单字段调整
- 模拟数据字段修正
- 选中标签显示字段更新

**主要更改：**
```javascript
// 表格列修复
<el-table-column prop="coinName1" label="钱币名称" min-width="200" />
<el-table-column prop="gradeScore" label="评级打分" width="100" />
<el-table-column prop="sendnum" label="送评单号" width="150" />

// 搜索表单修复
const searchForm = reactive({
  sendformNumber: '',
  serialNumber: '',
  coinName1: ''  // 原来是 itemName
});
```

## 数据绑定示例

### 在模板中使用字段
```vue
<template>
  <!-- 钱币基本信息 -->
  <div class="coin-info">
    <h3>{{ coin.coinName1 }}</h3>
    <p>编号: {{ coin.serialNumber }}</p>
    <p>等级: {{ coin.gradeScore }}</p>
    <p>真伪: {{ coin.authenticity }}</p>
    <p>送评单号: {{ coin.sendnum }}</p>
  </div>
  
  <!-- 钱币详细信息 -->
  <div class="coin-details">
    <p>类型: {{ coin.coinType }}</p>
    <p>年份: {{ coin.year }}</p>
    <p>面值: {{ coin.faceValue }}</p>
    <p>版别: {{ coin.version }}</p>
    <p>材质: {{ coin.material }}</p>
    <p>地区: {{ coin.region }}</p>
  </div>
</template>
```

### 在数据绑定中使用
```javascript
// 彩签元素数据绑定
const textElement = {
  type: 'text',
  text: '{{coinName1}}',
  dataBinding: 'coinName1',
  style: { fontSize: 16, color: '#333' }
};

const qrElement = {
  type: 'qrcode',
  dataBinding: 'diyCode',
  position: { left: 100, top: 50 }
};
```

## 注意事项

1. **字段兼容性**：部分字段可能为空，需要提供默认值
2. **数据类型**：注意 BigDecimal 类型字段的处理
3. **显示格式**：日期、金额等字段需要格式化显示
4. **多语言支持**：字段标签支持国际化

## ✅ 修复验证结果

### 开发服务器状态
- ✅ 服务器启动成功（端口 5174）
- ✅ 没有编译错误
- ✅ 所有字段映射修复完成

### 修复文件清单
1. ✅ `ColorLabelPreview.vue` - 预览组件字段修复
2. ✅ `ColorLabelDesigner.vue` - 设计器可用字段更新
3. ✅ `CoinSelectorDialog.vue` - 选择器表格和搜索字段修复
4. ✅ `index.vue` - 主入口预览表格字段修复
5. ✅ `api/index.js` - API路径修正

### 字段映射验证
| 组件 | 原字段 | 新字段 | 状态 |
|------|--------|--------|------|
| 预览组件 | `itemName` | `coinName1` | ✅ |
| 预览组件 | `gradeLevel` | `gradeScore` | ✅ |
| 预览组件 | `customerName` | `sendnum` | ✅ |
| 选择器 | `itemName` | `coinName1` | ✅ |
| 选择器 | `gradeLevel` | `gradeScore` | ✅ |
| 选择器 | `sendformNumber` | `sendnum` | ✅ |
| 主入口 | `itemName` | `coinName1` | ✅ |
| 主入口 | `gradeLevel` | `gradeScore` | ✅ |
| 主入口 | `customerName` | `sendnum` | ✅ |

## 测试建议

1. **字段显示测试**：验证所有字段在界面上正确显示
2. **数据绑定测试**：测试彩签元素的数据绑定功能
3. **搜索功能测试**：验证搜索表单使用正确字段
4. **预览功能测试**：确保预览数据使用正确字段映射
5. **API接口测试**：验证所有API路径正确（已添加 `/api` 前缀）

## 后续优化

1. **字段分组**：可以将字段按功能分组显示
2. **字段验证**：添加字段值的有效性验证
3. **默认值处理**：为空字段提供合理的默认值
4. **格式化显示**：对特殊字段进行格式化处理
5. **数据类型处理**：注意 BigDecimal 等特殊类型的前端处理

## 完成状态

🎉 **所有字段映射修复已完成！**

- ✅ 前端字段名称与后端 `PjOSendformItem` 实体完全对应
- ✅ API接口路径已修正
- ✅ 开发服务器运行正常
- ✅ 无编译错误
- ✅ 所有组件字段映射一致
